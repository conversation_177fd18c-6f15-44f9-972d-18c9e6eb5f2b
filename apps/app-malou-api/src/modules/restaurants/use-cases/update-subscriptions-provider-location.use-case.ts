import { inject, singleton } from 'tsyringe';

import { UpdateSubscriptionsProviderLocationBodyDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';
import { BusinessCategory, PlatformKey } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';

export interface UpdateSubscriptionsProviderLocationRequest extends UpdateSubscriptionsProviderLocationBodyDto {
    restaurantId: string;
}

@singleton()
export class UpdateSubscriptionsProviderLocationUseCase {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        @inject(InjectionToken.SubscriptionsProvider) private readonly _subscriptionsProvider: SubscriptionsProvider
    ) {}

    async execute(request: UpdateSubscriptionsProviderLocationRequest): Promise<void> {
        const { restaurantId, platformKey, newSubscriptionsProviderId } = request;

        const restaurant = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            projection: { type: 1, subscriptionsProviderId: 1 },
            options: { lean: true },
        });

        if (!restaurant?.type) {
            return;
        }

        const restaurantType = restaurant.type;

        if (!this._isConcernedBySubscriptionsProvider(platformKey, restaurantType)) {
            return;
        }

        logger.info('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Starting update process', {
            restaurantId,
            currentSubscriptionsProviderId: restaurant.subscriptionsProviderId,
            newSubscriptionsProviderId,
            platformKey,
            restaurantType,
        });

        try {
            if (restaurant.subscriptionsProviderId) {
                logger.info('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Clearing old subscriptions provider location', {
                    oldSubscriptionsProviderId: restaurant.subscriptionsProviderId,
                });

                await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                    subscriptionsProviderLocationId: restaurant.subscriptionsProviderId,
                    malouRestaurantId: null,
                });
            }

            logger.info('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Setting new subscriptions provider location', {
                newSubscriptionsProviderId,
                restaurantId,
            });

            await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                subscriptionsProviderLocationId: newSubscriptionsProviderId,
                malouRestaurantId: restaurantId,
            });

            await this._restaurantsRepository.updateOne({
                filter: { _id: toDbId(restaurantId) },
                update: { subscriptionsProviderId: newSubscriptionsProviderId },
            });

            logger.info('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Successfully updated subscriptions provider location', {
                restaurantId,
                newSubscriptionsProviderId,
            });
        } catch (error) {
            logger.error('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Failed to update subscriptions provider location', {
                restaurantId,
                newSubscriptionsProviderId,
                error: error instanceof Error ? error.message : 'Unknown error',
            });

            // If we fail after clearing the old one, we should try to restore it
            if (restaurant.subscriptionsProviderId) {
                try {
                    await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                        subscriptionsProviderLocationId: restaurant.subscriptionsProviderId,
                        malouRestaurantId: restaurantId,
                    });
                    logger.info('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Restored old subscriptions provider location after failure');
                } catch (restoreError) {
                    logger.error('[UPDATE_SUBSCRIPTIONS_PROVIDER_LOCATION] Failed to restore old subscriptions provider location', {
                        error: restoreError instanceof Error ? restoreError.message : 'Unknown error',
                    });
                }
            }

            throw error;
        }
    }

    private _isConcernedBySubscriptionsProvider(platformKey: PlatformKey, restaurantType: BusinessCategory): boolean {
        return (
            (platformKey === PlatformKey.FACEBOOK && restaurantType === BusinessCategory.BRAND) ||
            (platformKey === PlatformKey.GMB && restaurantType === BusinessCategory.LOCAL_BUSINESS)
        );
    }
}
