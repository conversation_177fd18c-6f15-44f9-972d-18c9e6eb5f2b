import { inject, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { BusinessCategory, PlatformKey } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { CommentsRepository } from ':modules/comments/comments.repository';
import MentionsRepository from ':modules/mentions/mentions.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsUseCases from ':modules/posts/posts.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class DeletePlatformUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _commentsRepository: CommentsRepository,
        private readonly _mentionsRepository: MentionsRepository,
        private readonly _postsUseCases: PostsUseCases,
        private readonly _restaurantsRepository: RestaurantsRepository,
        @inject(InjectionToken.SubscriptionsProvider) private readonly _subscriptionsProvider: SubscriptionsProvider
    ) {}

    async execute(platformId: string) {
        const platform = await this._platformsRepository.findOneOrFail({
            filter: { _id: toDbId(platformId) },
            projection: { key: 1, restaurantId: 1 },
            options: { lean: true },
        });

        const { subscriptionsProviderId, type: restaurantType } = await this._restaurantsRepository.findOneOrFail({
            filter: { _id: toDbId(platform.restaurantId) },
            projection: { subscriptionsProviderId: 1, type: 1 },
            options: { lean: true },
        });

        if (this._subscriptionShouldBeDeleted(platform.key, restaurantType) && subscriptionsProviderId) {
            await this._subscriptionsProvider.updateSubscriptionsProviderLocation({
                subscriptionsProviderLocationId: subscriptionsProviderId,
                malouRestaurantId: null,
            });
        }

        const { key: platformKey, restaurantId } = platform;

        await Promise.all([
            this._reviewsRepository.deleteMany({ filter: { key: platformKey, restaurantId } }),
            this._commentsRepository.deleteMany({ filter: { platformKey, restaurantId } }),
            this._mentionsRepository.deleteMany({ filter: { platformKey, restaurantId } }),
            this._postsUseCases.deleteManyPostsAndHandleSideEffects({ key: platformKey, restaurantId }),
            this._restaurantsRepository.deletePlatformAccess(restaurantId.toString(), platformKey),
        ]);

        const deletedPlatform = await this._platformsRepository.deleteOne({ filter: { _id: platformId } });

        return deletedPlatform;
    }

    private _subscriptionShouldBeDeleted(platformKey: PlatformKey, restaurantType: BusinessCategory): boolean {
        return (
            (platformKey === PlatformKey.FACEBOOK && restaurantType === BusinessCategory.BRAND) ||
            (platformKey === PlatformKey.GMB && restaurantType === BusinessCategory.LOCAL_BUSINESS)
        );
    }
}
